# RCM Chatbot

A modern, user-friendly chatbot UI for Revenue Cycle Management (RCM) built with React.js. This application provides an interactive interface for users to ask questions about healthcare revenue cycle management and receive expert responses with proper formatting and professional presentation.

![RCM Chatbot Screenshot](screenshot.png)

## Features

- **Streaming Responses**: Text appears word by word as it's generated, providing a more engaging user experience
- **Professional Formatting**: Responses include properly formatted bold text, numbered lists, and links
- **Healthcare/Finance Design Aesthetic**: Clean, professional UI with a color scheme suitable for healthcare applications
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **OpenAI Integration**: Powered by OpenAI's GPT models with a specialized RCM expert prompt
- **Markdown Support**: Properly renders formatted text with emphasis on important terms and concepts

## Technology Stack

- **Frontend**: React.js with Vite for fast development
- **Styling**: Styled Components for modular, maintainable CSS
- **AI Integration**: OpenAI API with streaming capabilities
- **Text Processing**: Custom text formatting for professional presentation
- **State Management**: React hooks for efficient state handling

## Getting Started

### Prerequisites

- Node.js (v14.0.0 or later)
- npm or yarn
- OpenAI API key

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/rcm-chatbot.git
   cd rcm-chatbot
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure your OpenAI API key:
   - Open `src/services/openaiService.js`
   - Replace `'your-api-key-here'` with your actual OpenAI API key
   - For production, use environment variables instead of hardcoding the API key

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Open your browser and navigate to `http://localhost:5173` (or the port shown in your terminal)

### Using the Chatbot

1. **Starting a Conversation**:
   - When you first open the application, you'll see a welcome message
   - Type your question about Revenue Cycle Management in the input field at the bottom
   - Press Enter or click the send button to submit your question

2. **Viewing Responses**:
   - The chatbot will display a typing indicator while generating a response
   - The response will appear word by word in real-time (streaming)
   - Important terms and concepts will be highlighted in bold
   - Numbered lists and bullet points will be properly formatted
   - Links to authoritative sources will be clickable and open in a new tab

3. **Continuing the Conversation**:
   - After receiving a response, you can ask follow-up questions
   - The chatbot maintains context from previous messages
   - You can scroll up to review the conversation history

4. **Example Questions**:
   - "What is Revenue Cycle Management?"
   - "Explain the steps in the healthcare billing process"
   - "What are common denial reasons in medical billing?"
   - "How can I improve clean claim rates?"
   - "What are the best practices for patient collections?"

5. **Troubleshooting**:
   - If the chatbot doesn't respond, check your OpenAI API key
   - Ensure your API key has sufficient credits
   - Check the browser console for any error messages
   - Restart the development server if needed

## Project Structure

```
rcm-chatbot/
├── public/              # Static assets
├── src/
│   ├── components/      # React components
│   │   ├── Chatbot.jsx  # Main chatbot component
│   │   ├── ChatInput.jsx # User input component
│   │   ├── Message.jsx  # Message display component
│   │   └── TypingIndicatorComponent.jsx # Typing animation
│   ├── services/        # API services
│   │   └── openaiService.js # OpenAI integration
│   ├── styles/          # Styled components
│   │   └── ChatbotStyles.js # UI styling
│   ├── App.jsx          # Main application component
│   ├── App.css          # Global styles
│   ├── main.jsx         # Application entry point
│   └── index.css        # Base styles
├── package.json         # Dependencies and scripts
└── vite.config.js       # Vite configuration
```

## Key Components

### Chatbot.jsx
The main component that manages the chat state, handles user interactions, and coordinates the streaming responses.

### Message.jsx
Renders individual messages with proper formatting, including HTML processing for bold text, numbered lists, and links.

### openaiService.js
Handles communication with the OpenAI API, including streaming responses and formatting instructions.

## Customization

### Styling
You can customize the appearance by modifying the `src/styles/ChatbotStyles.js` file. The color palette is defined at the top of the file:

```javascript
export const colors = {
  primary: '#1976d2',    // Professional blue
  secondary: '#f5f5f5',  // Light gray for background
  accent: '#2196f3',     // Lighter blue for accents
  // ... more colors
};
```

### OpenAI Model
You can change the OpenAI model by modifying the `model` parameter in `src/services/openaiService.js`:

```javascript
const response = await openai.chat.completions.create({
  model: 'gpt-4-turbo', // Change to your preferred model
  // ... other parameters
});
```

### RCM Expert Prompt
The chatbot's expertise and tone are defined by the `RCM_EXPERT_PROMPT` in `src/services/openaiService.js`. You can modify this prompt to change the chatbot's knowledge domain or response style.

### Text Formatting
The chatbot includes special formatting features that can be customized:

1. **Bold Text**: Important terms and concepts are automatically highlighted in bold. You can modify the list of terms in `src/components/Message.jsx`:
   ```javascript
   const importantTerms = [
     'Revenue Cycle Management', 'RCM', 'Patient Registration',
     // Add or remove terms here
   ];
   ```

2. **Numbered Lists**: The formatting for numbered lists can be customized in `src/styles/ChatbotStyles.js`:
   ```javascript
   .list-item {
     margin-bottom: 8px;
     padding-left: 8px;
     display: flex;
   }

   .list-number {
     font-weight: 600;
     color: ${colors.primary};
     margin-right: 8px;
     min-width: 20px;
   }
   ```

3. **Links**: The styling for links can be modified in `src/styles/ChatbotStyles.js`:
   ```javascript
   a {
     color: ${colors.accent};
     text-decoration: none;

     &:hover {
       text-decoration: underline;
     }
   }
   ```

4. **Streaming Speed**: The streaming behavior is controlled by the OpenAI API. The chatbot displays text as it's received from the API.

## Production Deployment

For production deployment:

1. Build the application:
   ```bash
   npm run build
   ```

2. The build output will be in the `dist` directory, which can be deployed to any static hosting service.

3. Important security considerations:
   - Move the OpenAI API key to environment variables
   - Implement server-side API calls instead of client-side calls
   - Set up proper authentication and rate limiting

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Troubleshooting

### Common Issues

1. **API Key Issues**:
   - Error: "Authentication error" or "Invalid API key"
   - Solution: Double-check your OpenAI API key in `src/services/openaiService.js`
   - Make sure your API key has not expired and has sufficient credits

2. **Streaming Not Working**:
   - Issue: Responses appear all at once instead of streaming
   - Solution: Ensure the `stream: true` parameter is set in the OpenAI API call
   - Check that the streaming handler in `Chatbot.jsx` is properly updating state

3. **Formatting Problems**:
   - Issue: Raw asterisks or markdown syntax visible in responses
   - Solution: Check the text formatting function in `Message.jsx`
   - Ensure the HTML rendering is properly configured

4. **Development Server Issues**:
   - Issue: "Port already in use" error
   - Solution: Kill any running processes on the port or use a different port
   - Command: `npm run dev -- --port 3000` to specify a different port

5. **Browser Compatibility**:
   - Issue: UI looks broken in certain browsers
   - Solution: Add appropriate polyfills or browser-specific styles
   - Test in multiple browsers and add fallbacks as needed

### Getting Help

If you encounter issues not covered here, please:
1. Check the browser console for error messages
2. Review the OpenAI API documentation
3. Open an issue on the GitHub repository with detailed information about the problem

## Acknowledgments

- OpenAI for providing the GPT models
- React and Vite teams for the excellent development tools
- Styled Components for the styling solution
