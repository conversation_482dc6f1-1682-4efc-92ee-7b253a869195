import React from 'react';
import { UserMessage, BotMessage, MessageTime, MessageSender, StreamingCursor, MarkdownContent } from '../styles/ChatbotStyles';

// Function to format text with HTML for proper display
const formatTextWithHTML = (text) => {
  let formattedText = text;

  // Handle markdown headers (###) - convert to bold headings
  formattedText = formattedText.replace(/^###\s*(.+?)$/gm, '<h3><strong>$1</strong></h3>');

  // Handle numbered items with ### markers
  formattedText = formattedText.replace(/^(\d+)\.\s*###\s*(.+?)$/gm, '<div class="list-item"><span class="list-number">$1.</span> <strong>$2</strong></div>');

  // Remove any standalone asterisks or broken markdown
  formattedText = formattedText.replace(/\*(?!\*)/g, '');
  formattedText = formattedText.replace(/(?<!\*)\*/g, '');

  // Convert markdown bold to HTML strong tags
  formattedText = formattedText.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

  // Handle any unclosed bold tags
  formattedText = formattedText.replace(/\*\*([^*]+)(?!\*\*)/g, '<strong>$1</strong>');

  // Format numbered lists (that don't have ### markers)
  formattedText = formattedText.replace(/^(\d+)\.\s+(?!<)(.+?)$/gm, '<div class="list-item"><span class="list-number">$1.</span> $2</div>');

  // Format bullet points
  formattedText = formattedText.replace(/•\s+(.+?)(?=\n•|\n\n|$)/gs, '<div class="bullet-item">• $1</div>');

  // Format links
  formattedText = formattedText.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

  // Format paragraphs, but skip lines that are already formatted
  formattedText = formattedText.replace(/^(.+?)$/gm, (match) => {
    // Skip if it's already a formatted element
    if (match.startsWith('<div') ||
        match.startsWith('<h3') ||
        match.startsWith('<strong') ||
        match.startsWith('<a') ||
        match.trim() === '') {
      return match;
    }
    return `<p>${match}</p>`;
  });

  // Clean up any remaining markdown symbols
  formattedText = formattedText.replace(/\*\*/g, '');
  formattedText = formattedText.replace(/###/g, '');

  return formattedText;
};

const Message = ({ message }) => {
  const { text, isUser, timestamp, isStreaming } = message;

  // Format the timestamp
  const formattedTime = new Date(timestamp).toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit'
  });

  // Process the text for bot messages to enhance formatting
  const formattedHTML = !isUser ? formatTextWithHTML(text) : text;

  return isUser ? (
    <UserMessage>
      <MessageSender>You</MessageSender>
      {text}
      <MessageTime>{formattedTime}</MessageTime>
    </UserMessage>
  ) : (
    <BotMessage>
      <MessageSender>RCM Assistant</MessageSender>
      <MarkdownContent>
        <div dangerouslySetInnerHTML={{ __html: formattedHTML }} />
      </MarkdownContent>
      {isStreaming && <StreamingCursor />}
      <MessageTime>{formattedTime}</MessageTime>
    </BotMessage>
  );
};

export default Message;
