# RCM Chatbot Deployment Guide

## Overview
This guide explains how to deploy the RCM Chatbot to Render with proper OpenAI API integration.

## Architecture Changes
The application has been restructured to fix the OpenAI API issues:

### Before (Issues):
- ❌ OpenAI API key hardcoded in frontend code
- ❌ Client-side API calls with `dangerouslyAllowBrowser: true`
- ❌ Invalid OpenAI model name (`gpt-4.1`)
- ❌ No backend server
- ❌ Security vulnerabilities

### After (Fixed):
- ✅ Backend Express.js server handles OpenAI API calls
- ✅ API key stored securely in environment variables
- ✅ Valid OpenAI model (`gpt-4.1`)
- ✅ Frontend calls backend API instead of OpenAI directly
- ✅ Proper CORS configuration
- ✅ Production-ready deployment

## Local Development Setup

1. **Install Dependencies**:
   ```bash
   cd rcm-chatbot
   npm install
   ```

2. **Environment Configuration**:
   ```bash
   cp .env.example .env
   ```
   Edit `.env` and add your OpenAI API key:
   ```
   OPENAI_API_KEY=your_actual_openai_api_key_here
   PORT=5000
   NODE_ENV=development
   ```

3. **Run Development Server**:
   ```bash
   npm run dev
   ```
   This will start both the backend server (port 5000) and frontend dev server (port 5173).

## Render Deployment

### Step 1: Prepare Your Repository
1. Commit all changes to your Git repository
2. Push to GitHub/GitLab

### Step 2: Create Render Service
1. Go to [Render Dashboard](https://dashboard.render.com/)
2. Click "New +" → "Web Service"
3. Connect your repository
4. Configure the service:
   - **Name**: `rcm-chatbot`
   - **Environment**: `Node`
   - **Build Command**: `npm install && npm run build`
   - **Start Command**: `npm start`
   - **Plan**: Free (or paid for better performance)

### Step 3: Set Environment Variables
In the Render dashboard, go to Environment tab and add:
- **OPENAI_API_KEY**: Your actual OpenAI API key
- **NODE_ENV**: `production`
- **PORT**: Leave empty (Render will set this automatically)

### Step 4: Deploy
1. Click "Create Web Service"
2. Wait for the build and deployment to complete
3. Your app will be available at the provided Render URL

## Environment Variables

### Required:
- `OPENAI_API_KEY`: Your OpenAI API key (get from https://platform.openai.com/api-keys)

### Optional:
- `PORT`: Server port (default: 5000, Render sets this automatically)
- `NODE_ENV`: Environment mode (development/production)

## API Endpoints

The backend provides these endpoints:

- `POST /api/chat`: Non-streaming chat completion
- `POST /api/chat/stream`: Streaming chat completion
- `GET /api/health`: Health check
- `GET /*`: Serves the React frontend

## Troubleshooting

### Common Issues:

1. **"OpenAI API Key not configured"**:
   - Ensure `OPENAI_API_KEY` is set in Render environment variables
   - Check the environment variables tab in Render dashboard

2. **"Failed to fetch" errors**:
   - Check if the backend server is running
   - Verify API endpoints are accessible
   - Check browser console for CORS errors

3. **Build failures**:
   - Ensure all dependencies are in `package.json`
   - Check build logs in Render dashboard
   - Verify Node.js version compatibility

4. **OpenAI API errors**:
   - Verify your API key is valid and has credits
   - Check OpenAI API status page
   - Ensure you're using a valid model name

### Debugging Steps:

1. **Check Health Endpoint**:
   Visit `https://your-app.onrender.com/api/health` to verify backend is running

2. **Check Logs**:
   - In Render dashboard, go to "Logs" tab
   - Look for server startup messages and error logs

3. **Test API Locally**:
   ```bash
   curl -X POST http://localhost:5000/api/chat \
     -H "Content-Type: application/json" \
     -d '{"messages":[{"text":"Hello","isUser":true}]}'
   ```

## Security Notes

- ✅ API key is now stored securely in environment variables
- ✅ No sensitive data exposed in frontend code
- ✅ CORS properly configured
- ✅ Server-side API calls only

## Performance Optimization

For production use, consider:
- Upgrading to a paid Render plan for better performance
- Implementing rate limiting
- Adding request caching
- Using a CDN for static assets
- Monitoring and logging

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Render deployment logs
3. Verify OpenAI API key and credits
4. Check network connectivity and CORS settings
