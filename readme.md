# React and Node.js Requirements

This document outlines all the dependencies required for the RCM Chatbot project.

## Node.js Requirements

- **Node.js**: v16.0.0 or later
- **npm**: v7.0.0 or later (comes with Node.js)

## Core Dependencies

| Package | Version | Purpose |
|---------|---------|---------|
| react | ^19.1.0 | Core React library |
| react-dom | ^19.1.0 | React DOM rendering |
| styled-components | ^6.1.17 | CSS-in-JS styling solution |
| openai | ^4.97.0 | OpenAI API client for JavaScript |
| react-markdown | ^10.1.0 | Markdown rendering for React |

## Development Dependencies

| Package | Version | Purpose |
|---------|---------|---------|
| vite | ^6.3.5 | Fast build tool and development server |
| @vitejs/plugin-react | ^4.4.1 | React plugin for Vite |
| eslint | ^9.25.0 | JavaScript linting utility |
| @eslint/js | ^9.25.0 | ESLint JavaScript configuration |
| eslint-plugin-react-hooks | ^5.2.0 | React Hooks linting rules |
| eslint-plugin-react-refresh | ^0.4.19 | React Refresh linting rules |
| @types/react | ^19.1.2 | TypeScript definitions for React |
| @types/react-dom | ^19.1.2 | TypeScript definitions for React DOM |
| globals | ^16.0.0 | Global variables for ESLint |

## Installation Instructions

1. Install Node.js and npm from [nodejs.org](https://nodejs.org/)

2. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/rcm-chatbot.git
   cd rcm-chatbot
   ```

3. Install all dependencies:
   ```bash
   npm install
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

## Optional Dependencies

These packages are not currently included but may be useful for extending the project:

| Package | Purpose |
|---------|---------|
| axios | Promise-based HTTP client (alternative to fetch) |
| react-router-dom | Routing for React applications |
| redux / @reduxjs/toolkit | State management (for larger applications) |
| jest | Testing framework |
| @testing-library/react | React component testing utilities |
| framer-motion | Animation library for React |
| tailwindcss | Utility-first CSS framework (alternative to styled-components) |

## Environment Requirements

- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection for OpenAI API calls
- OpenAI API key with appropriate permissions
