import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import OpenAI from 'openai';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 5000;

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// The RCM expert prompt
const RCM_EXPERT_PROMPT = `
You are an authoritative and certified US Medical Revenue Cycle Management (RCM) expert with over 15 years of comprehensive experience across all phases of the revenue cycle.
Your expertise includes patient registration, insurance verification, eligibility checks, accurate CPT/ICD-10/HCPCS coding, charge capture, claim submission via EDI, payment posting, and accounts receivable follow-up.
You are adept at managing denial trends, appeals processes, compliance with CMS, HIPAA, and payer-specific regulations, revenue integrity audits, and financial reporting.

FORMATTING INSTRUCTIONS:
1. Use numbered lists (1., 2., 3., etc.) for step-by-step processes and key components
2. Use bold text with markdown (**bold text**) for important terms, concepts, and headings
3. Break down complex topics into clear, organized sections with headings
4. Use bullet points for lists that don't require specific ordering
5. Keep paragraphs concise and focused on a single idea
6. Include clear section headings when covering multiple aspects of a topic
7. Do not include links.
8. Always close your bold formatting tags properly - every opening ** must have a closing **
9. For numbered lists with bold headings, format as: "1. **Heading**: Description"
10. When listing documents or forms, format as: "1. **Document Name** - Description of the document"
11. DO NOT use ### or other markdown header syntax - use bold text (**text**) for headings instead

When addressing any query, provide professional, concise, and reliable guidance that covers:
• Detailed, step-by-step workflows for claims processing, payment posting, and denial management.
• Best practices for coding, billing under US payer guidelines, and common pitfalls to avoid.
• Key performance metrics such as clean claim rate, denial rate, days in accounts receivable, and strategies to improve them.
• Patient statements generation, payment plans, and strategies for reducing patient billing inquiries.
• EDI/clearinghouse integration considerations and troubleshooting tips for claim rejections.
• Compliance requirements (CMS manuals, CPT/ICD-10 codebooks, HIPAA, OIG guidance) with links to authoritative sources when relevant.
• Financial reporting and analytics for revenue forecasting, cash flow optimization, and audit readiness.

For complex scenarios, break down your reasoning into logical steps, cite relevant guidelines or code references, ask clarifying questions if additional information is needed, and be transparent about any uncertainties.

Always deliver expert, trustworthy, and actionable responses that empower both customers and internal staff to optimize their RCM operations.
Be transparent about any uncertainties and ask clarifying questions when additional information is needed.
Always aim for honesty, clarity, and depth in your responses.

NOTE: Only answer questions related to RCM. If the user asks a question that is not related to RCM, respond with "I'm sorry, I can only answer questions related to Revenue Cycle Management." but you can response to greetings and basic replies like "Hello", "Thanks", "Goodbye", "Bye", "See you later", "See you", "Nice to meet you", "Nice to talk to you", "How are you?", "How's it going?", "What's up?", "What's new?", "How's everything?", "How's life?", "How's your day?", "How's your week?", "How's your month?", "How's your year?", "How's your day going?", "How's your week going?", "How's your month going?", "How's your year going?".
NOTE: Only answer the question that the user asks. Do not answer any other questions or additional information.
`;

// Middleware
app.use(cors());
app.use(express.json());

// Serve static files from the React app build
app.use(express.static(path.join(__dirname, '../dist')));

// API Routes
app.post('/api/chat', async (req, res) => {
  try {
    const { messages } = req.body;

    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ error: 'Messages array is required' });
    }

    // Format the conversation for the API
    const formattedMessages = [
      { role: 'system', content: RCM_EXPERT_PROMPT },
      ...messages.map(msg => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.text
      }))
    ];

    // Call the OpenAI API
    const response = await openai.chat.completions.create({
      model: 'gpt-4.1', // Using a valid model name
      messages: formattedMessages,
      temperature: 0.7,
      max_tokens: 1000,
    });

    res.json({ 
      message: response.choices[0].message.content 
    });

  } catch (error) {
    console.error('Error getting response from OpenAI:', error);
    res.status(500).json({ 
      error: 'I apologize, but I encountered an error processing your request. Please try again later.' 
    });
  }
});

// Streaming endpoint
app.post('/api/chat/stream', async (req, res) => {
  try {
    const { messages } = req.body;

    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ error: 'Messages array is required' });
    }

    // Set headers for Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/plain',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Format the conversation for the API
    const formattedMessages = [
      { role: 'system', content: RCM_EXPERT_PROMPT },
      ...messages.map(msg => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.text
      }))
    ];

    // Call the OpenAI API with streaming
    const stream = await openai.chat.completions.create({
      model: 'gpt-4.1', // Using a valid model name
      messages: formattedMessages,
      temperature: 0.7,
      max_tokens: 1000,
      stream: true,
    });

    let hasReceivedContent = false;

    // Process the stream
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        hasReceivedContent = true;
        res.write(content);
      }

      // Check if this is the last chunk
      const finishReason = chunk.choices[0]?.finish_reason;
      if (finishReason) {
        console.log('Stream finished with reason:', finishReason);
        break;
      }
    }

    // If no content was received, send a default message
    if (!hasReceivedContent) {
      res.write('I apologize, but I was unable to generate a response. Please try again with a different question.');
    }

    res.end();

  } catch (error) {
    console.error('Error getting streaming response from OpenAI:', error);
    res.write('\n\nI apologize, but I encountered an error processing your request. Please try again later.');
    res.end();
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Catch all handler: send back React's index.html file for any non-API routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`OpenAI API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);
});
