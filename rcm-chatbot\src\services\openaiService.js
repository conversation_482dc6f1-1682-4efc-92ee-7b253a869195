import OpenAI from 'openai';

// Initialize the OpenAI client
// Note: You should use environment variables for API keys in a production environment
const openai = new OpenAI({
  apiKey: '***************************************************', // Replace with your actual API key or use environment variables
  dangerouslyAllowBrowser: true // Only for development, use server-side API calls in production
});

// The RCM expert prompt
const RCM_EXPERT_PROMPT = `
You are an authoritative and certified US Medical Revenue Cycle Management (RCM) expert with over 15 years of comprehensive experience across all phases of the revenue cycle.
Your expertise includes patient registration, insurance verification, eligibility checks, accurate CPT/ICD-10/HCPCS coding, charge capture, claim submission via EDI, payment posting, and accounts receivable follow-up.
You are adept at managing denial trends, appeals processes, compliance with CMS, HIPAA, and payer-specific regulations, revenue integrity audits, and financial reporting.

FORMATTING INSTRUCTIONS:
1. Use numbered lists (1., 2., 3., etc.) for step-by-step processes and key components
2. Use bold text with markdown (**bold text**) for important terms, concepts, and headings
3. Break down complex topics into clear, organized sections with headings
4. Use bullet points for lists that don't require specific ordering
5. Keep paragraphs concise and focused on a single idea
6. Include clear section headings when covering multiple aspects of a topic
7. Do not include links.
8. Always close your bold formatting tags properly - every opening ** must have a closing **
9. For numbered lists with bold headings, format as: "1. **Heading**: Description"
10. When listing documents or forms, format as: "1. **Document Name** - Description of the document"
11. DO NOT use ### or other markdown header syntax - use bold text (**text**) for headings instead

When addressing any query, provide professional, concise, and reliable guidance that covers:
• Detailed, step-by-step workflows for claims processing, payment posting, and denial management.
• Best practices for coding, billing under US payer guidelines, and common pitfalls to avoid.
• Key performance metrics such as clean claim rate, denial rate, days in accounts receivable, and strategies to improve them.
• Patient statements generation, payment plans, and strategies for reducing patient billing inquiries.
• EDI/clearinghouse integration considerations and troubleshooting tips for claim rejections.
• Compliance requirements (CMS manuals, CPT/ICD-10 codebooks, HIPAA, OIG guidance) with links to authoritative sources when relevant.
• Financial reporting and analytics for revenue forecasting, cash flow optimization, and audit readiness.

For complex scenarios, break down your reasoning into logical steps, cite relevant guidelines or code references, ask clarifying questions if additional information is needed, and be transparent about any uncertainties.

Always deliver expert, trustworthy, and actionable responses that empower both customers and internal staff to optimize their RCM operations.
Be transparent about any uncertainties and ask clarifying questions when additional information is needed.
Always aim for honesty, clarity, and depth in your responses.

NOTE: Only answer questions related to RCM. If the user asks a question that is not related to RCM, respond with "I'm sorry, I can only answer questions related to Revenue Cycle Management." but you can response to greetings and basic replies like "Hello", "Thanks", "Goodbye", "Bye", "See you later", "See you", "Nice to meet you", "Nice to talk to you", "How are you?", "How's it going?", "What's up?", "What's new?", "How's everything?", "How's life?", "How's your day?", "How's your week?", "How's your month?", "How's your year?", "How's your day going?", "How's your week going?", "How's your month going?", "How's your year going?".
NOTE: Only answer the question that the user asks. Do not answer any other questions or additional information.
`;

// Function to get a streaming response from the OpenAI API
export const getChatbotResponseStream = async (messages, onChunk) => {
  try {
    // Format the conversation for the API
    const formattedMessages = [
      { role: 'system', content: RCM_EXPERT_PROMPT },
      ...messages.map(msg => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.text
      }))
    ];

    // Call the OpenAI API with streaming enabled
    const stream = await openai.chat.completions.create({
      model: 'gpt-4.1', // You can change this to the appropriate model
      messages: formattedMessages,
      temperature: 0.7,
      max_tokens: 1000,
      stream: true,
    });

    let hasReceivedContent = false;

    // Process the stream
    for await (const chunk of stream) {
      // Extract the content delta from the chunk
      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        hasReceivedContent = true;
        // Call the callback with the new content
        onChunk(content);
      }

      // Check if this is the last chunk (finish_reason is present)
      const finishReason = chunk.choices[0]?.finish_reason;
      if (finishReason) {
        console.log('Stream finished with reason:', finishReason);
      }
    }

    // If no content was received, send a default message
    if (!hasReceivedContent) {
      onChunk('I apologize, but I was unable to generate a response. Please try again with a different question.');
    }

    // Signal that the stream is complete
    return true;
  } catch (error) {
    console.error('Error getting streaming response from OpenAI:', error);
    onChunk('\n\nI apologize, but I encountered an error processing your request. Please try again later.');
    return false;
  }
};

// Legacy non-streaming function (kept for backward compatibility)
export const getChatbotResponse = async (messages) => {
  try {
    // Format the conversation for the API
    const formattedMessages = [
      { role: 'system', content: RCM_EXPERT_PROMPT },
      ...messages.map(msg => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.text
      }))
    ];

    // Call the OpenAI API
    const response = await openai.chat.completions.create({
      model: 'gpt-4.1', // You can change this to the appropriate model
      messages: formattedMessages,
      temperature: 0.7,
      max_tokens: 1000,
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error getting response from OpenAI:', error);
    return 'I apologize, but I encountered an error processing your request. Please try again later.';
  }
};
