// API configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || '/api';

// Function to get a streaming response from the backend API
export const getChatbotResponseStream = async (messages, onChunk) => {
  try {
    const response = await fetch(`${API_BASE_URL}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messages }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let hasReceivedContent = false;

    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      if (chunk) {
        hasReceivedContent = true;
        onChunk(chunk);
      }
    }

    // If no content was received, send a default message
    if (!hasReceivedContent) {
      onChunk('I apologize, but I was unable to generate a response. Please try again with a different question.');
    }

    return true;
  } catch (error) {
    console.error('Error getting streaming response from backend:', error);
    onChunk('\n\nI apologize, but I encountered an error processing your request. Please try again later.');
    return false;
  }
};

// Legacy non-streaming function (kept for backward compatibility)
export const getChatbotResponse = async (messages) => {
  try {
    const response = await fetch(`${API_BASE_URL}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messages }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.message;
  } catch (error) {
    console.error('Error getting response from backend:', error);
    return 'I apologize, but I encountered an error processing your request. Please try again later.';
  }
};
