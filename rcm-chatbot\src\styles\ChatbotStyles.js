import styled, { keyframes } from 'styled-components';

// Color palette for healthcare/finance aesthetic
export const colors = {
  primary: '#1976d2', // Professional blue
  secondary: '#f5f5f5', // Light gray for background
  accent: '#2196f3', // Lighter blue for accents
  text: '#333333', // Dark gray for text
  lightText: '#757575', // Medium gray for secondary text
  userBubble: '#e3f2fd', // Light blue for user messages
  botBubble: '#ffffff', // White for bot messages
  border: '#e0e0e0', // Light gray for borders
  shadow: 'rgba(0, 0, 0, 0.1)', // Shadow color
  success: '#4caf50', // Green for success indicators
  error: '#f44336', // Red for error messages
  cursor: '#1976d2', // Cursor color (same as primary)
};

// Main container for the chatbot
export const ChatbotContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 800px;
  height: 600px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px ${colors.shadow};
  background-color: ${colors.secondary};
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;

  @media (max-width: 850px) {
    height: 100vh;
    max-width: 100%;
    border-radius: 0;
  }
`;

// Header of the chatbot
export const ChatHeader = styled.div`
  background-color: ${colors.primary};
  color: white;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px ${colors.shadow};
  position: relative;
  z-index: 10;
`;

export const ChatTitle = styled.h1`
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
`;

// Container for the chat messages
export const MessagesContainer = styled.div`
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: ${colors.secondary};
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: ${colors.secondary};
  }

  &::-webkit-scrollbar-thumb {
    background-color: ${colors.border};
    border-radius: 20px;
  }
`;

// Base message bubble
const MessageBubble = styled.div`
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 1px 2px ${colors.shadow};
  line-height: 1.5;
  position: relative;
  word-wrap: break-word;
`;

// User message bubble
export const UserMessage = styled(MessageBubble)`
  align-self: flex-end;
  background-color: ${colors.userBubble};
  color: ${colors.text};
  border-bottom-right-radius: 4px;
`;

// Bot message bubble
export const BotMessage = styled(MessageBubble)`
  align-self: flex-start;
  background-color: ${colors.botBubble};
  color: ${colors.text};
  border-bottom-left-radius: 4px;
  border: 1px solid ${colors.border};
`;

// Timestamp for messages
export const MessageTime = styled.span`
  font-size: 0.7rem;
  color: ${colors.lightText};
  margin-top: 4px;
  display: block;
  text-align: right;
`;

// Input area container
export const InputContainer = styled.div`
  display: flex;
  padding: 16px;
  background-color: white;
  border-top: 1px solid ${colors.border};
  align-items: center;
`;

// Text input
export const Input = styled.input`
  flex: 1;
  padding: 12px 16px;
  border: 1px solid ${colors.border};
  border-radius: 24px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s;

  &:focus {
    border-color: ${colors.accent};
  }
`;

// Send button
export const SendButton = styled.button`
  background-color: ${colors.primary};
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-left: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${colors.accent};
  }

  &:disabled {
    background-color: ${colors.border};
    cursor: not-allowed;
  }
`;

// Typing indicator
export const TypingIndicator = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: ${colors.botBubble};
  border-radius: 18px;
  align-self: flex-start;
  margin-top: 8px;
  box-shadow: 0 1px 2px ${colors.shadow};

  span {
    width: 8px;
    height: 8px;
    margin: 0 2px;
    background-color: ${colors.lightText};
    border-radius: 50%;
    display: inline-block;
    opacity: 0.4;

    &:nth-child(1) {
      animation: typing 1s infinite 0s;
    }

    &:nth-child(2) {
      animation: typing 1s infinite 0.2s;
    }

    &:nth-child(3) {
      animation: typing 1s infinite 0.4s;
    }
  }

  @keyframes typing {
    0% {
      opacity: 0.4;
      transform: translateY(0);
    }
    50% {
      opacity: 1;
      transform: translateY(-4px);
    }
    100% {
      opacity: 0.4;
      transform: translateY(0);
    }
  }
`;

// Message metadata (sender name, etc.)
export const MessageSender = styled.div`
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 4px;
  color: ${colors.lightText};
`;

// Welcome message container
export const WelcomeContainer = styled.div`
  text-align: center;
  margin: 20px 0;
  padding: 0 20px;

  h2 {
    color: ${colors.primary};
    margin-bottom: 12px;
  }

  p {
    color: ${colors.lightText};
    line-height: 1.5;
  }
`;

// Blinking cursor animation
const blink = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
`;

// Streaming cursor that appears at the end of streaming text
export const StreamingCursor = styled.span`
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: ${colors.cursor};
  margin-left: 2px;
  vertical-align: middle;
  animation: ${blink} 1s infinite;
`;

// Styled container for markdown content
export const MarkdownContent = styled.div`
  line-height: 1.6;

  p {
    margin-bottom: 12px;
  }

  .list-item {
    margin-bottom: 12px;
    padding-left: 8px;
    display: flex;
    align-items: flex-start;
  }

  .list-number {
    font-weight: 600;
    color: ${colors.primary};
    margin-right: 8px;
    min-width: 20px;
  }

  .bullet-item {
    margin-bottom: 8px;
    padding-left: 20px;
  }

  ul, ol {
    margin-left: 20px;
    margin-bottom: 12px;
  }

  li {
    margin-bottom: 6px;
  }

  strong {
    font-weight: 600;
    color: ${colors.primary};
    display: inline-block;
    margin-bottom: 4px;
  }

  h1, h2, h3, h4, h5, h6 {
    margin-top: 16px;
    margin-bottom: 12px;
    font-weight: 600;
    color: ${colors.primary};
    line-height: 1.3;
  }

  h3 {
    font-size: 1.1rem;
    border-bottom: 1px solid ${colors.border};
    padding-bottom: 6px;
  }

  a {
    color: ${colors.accent};
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 4px;
    font-family: monospace;
  }

  blockquote {
    border-left: 4px solid ${colors.border};
    padding-left: 16px;
    margin-left: 0;
    color: ${colors.lightText};
  }

  /* Special styling for document lists */
  .list-item strong {
    display: block;
    margin-bottom: 4px;
  }
`;
