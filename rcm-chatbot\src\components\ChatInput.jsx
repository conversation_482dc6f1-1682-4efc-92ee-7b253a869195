import React, { useState, useRef, useEffect } from 'react';
import { InputContainer, Input, SendButton } from '../styles/ChatbotStyles';

const ChatInput = ({ onSendMessage, isLoading }) => {
  const [message, setMessage] = useState('');
  const inputRef = useRef(null);

  // Focus the input field when the component mounts
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Focus the input field after sending a message or when loading state changes
  useEffect(() => {
    if (!isLoading) {
      inputRef.current?.focus();
    }
  }, [isLoading]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim() && !isLoading) {
      onSendMessage(message);
      setMessage('');
      // Focus the input field after sending a message
      setTimeout(() => {
        inputRef.current?.focus();
      }, 0);
    }
  };

  return (
    <InputContainer as="form" onSubmit={handleSubmit}>
      <Input
        ref={inputRef}
        type="text"
        placeholder="Type your question about RCM..."
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        disabled={isLoading}
        autoFocus
      />
      <SendButton type="submit" disabled={!message.trim() || isLoading}>
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" fill="white" />
        </svg>
      </SendButton>
    </InputContainer>
  );
};

export default ChatInput;
