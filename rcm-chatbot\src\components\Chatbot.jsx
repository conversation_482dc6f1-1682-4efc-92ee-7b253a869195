import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Chat<PERSON><PERSON><PERSON>,
  MessagesContainer,
  WelcomeContainer
} from '../styles/ChatbotStyles';
import Message from './Message';
import ChatInput from './ChatInput';
import TypingIndicatorComponent from './TypingIndicatorComponent';
import { getChatbotResponseStream } from '../services/openaiService';

const Chatbot = () => {
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState(null);
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);

  // <PERSON><PERSON> scrolling behavior
  useEffect(() => {
    if (shouldScrollToBottom && !userHasScrolled) {
      scrollToBottom();
    }
  }, [messages, streamingMessage?.text, shouldScrollToBottom, userHasScrolled]);

  // Add scroll event listener to detect user scrolling
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50;
      
      // If user scrolls up, mark as scrolled
      if (!isAtBottom) {
        setUserHasScrolled(true);
      } else {
        // If user scrolls back to bottom, reset the flag
        setUserHasScrolled(false);
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (text) => {
    // If there's already a streaming message, add it to the messages array
    // before starting a new conversation
    if (streamingMessage) {
      // Add the current streaming message to the messages array if it's from the bot
      if (!streamingMessage.isUser) {
        // Always add the current streaming message to the history before starting a new conversation
        setMessages(prevMessages => [...prevMessages, { ...streamingMessage, isStreaming: false }]);
      }
    }

    // Add user message to chat
    const userMessage = {
      text,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prevMessages => [...prevMessages, userMessage]);
    setIsLoading(true);
    
    // Reset scroll behavior when user sends a new message
    setShouldScrollToBottom(true);
    setUserHasScrolled(false);

    // Create an empty bot message that will be updated as the stream comes in
    const initialBotMessage = {
      text: '',
      isUser: false,
      timestamp: new Date(),
      isStreaming: true
    };

    setStreamingMessage(initialBotMessage);

    try {
      // Get streaming response from OpenAI
      const streamComplete = await getChatbotResponseStream([...messages, userMessage], (chunk) => {
        // Update the streaming message with each new chunk
        setStreamingMessage(prevMessage => ({
          ...prevMessage,
          text: prevMessage.text + chunk
        }));
      });

      if (streamComplete) {
        // When streaming is complete, just update the streaming message to show it's no longer streaming
        // but DON'T add it to the messages array to avoid duplication
        setStreamingMessage(prevStreamingMessage => {
          if (prevStreamingMessage) {
            return {
              ...prevStreamingMessage,
              isStreaming: false
            };
          }
          return null;
        });
      }
    } catch (error) {
      console.error('Error getting response:', error);

      // Create an error message and add it to the messages array
      const errorMessage = {
        text: 'I apologize, but I encountered an error processing your request. Please try again later.',
        isUser: false,
        timestamp: new Date(),
        isStreaming: false
      };

      // Add the error message to the messages array
      setMessages(prevMessages => [...prevMessages, errorMessage]);

      // Clear the streaming message
      setStreamingMessage(null);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ChatbotContainer>
      <ChatHeader>
        <ChatTitle>RCM Assistant</ChatTitle>
        {userHasScrolled && (
          <button 
            onClick={() => {
              scrollToBottom();
              setUserHasScrolled(false);
            }}
            style={{
              background: 'rgba(255, 255, 255, 0.3)',
              border: 'none',
              borderRadius: '50%',
              width: '32px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              fontSize: '18px',
            }}
          >
            ↓
          </button>
        )}
      </ChatHeader>

      <MessagesContainer ref={messagesContainerRef}>
        {messages.length === 0 && !streamingMessage ? (
          <WelcomeContainer>
            <h2>Welcome to RCM Assistant</h2>
            <p>
              I'm your Revenue Cycle Management expert. Ask me anything about medical billing,
              coding, claims processing, denial management, or any other RCM-related topics.
            </p>
          </WelcomeContainer>
        ) : (
          <>
            {/* Display all messages in the conversation history */}
            {messages.map((message, index) => (
              <Message key={index} message={message} />
            ))}

            {/* Display the streaming message if it exists */}
            {streamingMessage && (
              <Message message={streamingMessage} />
            )}
          </>
        )}

        {isLoading && !streamingMessage && <TypingIndicatorComponent />}
        <div ref={messagesEndRef} />
      </MessagesContainer>

      <ChatInput 
        onSendMessage={(text) => {
          // Reset scroll behavior when user sends a new message
          setShouldScrollToBottom(true);
          setUserHasScrolled(false);
          handleSendMessage(text);
        }} 
        isLoading={isLoading} 
      />
    </ChatbotContainer>
  );
};

export default Chatbot;
