* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: '<PERSON><PERSON>', 'Segoe UI', <PERSON>l, sans-serif;
  background-color: #f9f9f9;
  color: #333;
}

#root {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.app-container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

@media (max-width: 850px) {
  .app-container {
    padding: 0;
  }
}
