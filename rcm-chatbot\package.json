{"name": "rcm-chatbot", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "client": "vite", "server": "node server/index.js", "build": "vite build", "start": "node server/index.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"openai": "^4.97.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "styled-components": "^6.1.17", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "concurrently": "^8.2.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}