:root {
  font-family: '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #333333;
  background-color: #f9f9f9;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

a {
  color: #1976d2;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  cursor: pointer;
  font-family: inherit;
}
